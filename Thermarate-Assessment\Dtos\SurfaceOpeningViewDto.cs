﻿using System;
using Newtonsoft.Json;

namespace RediSoftware.Dtos
{
    /// <summary>
    /// Dto for view RSS_SurfaceOpeningView
    /// </summary>
    public class SurfaceOpeningViewDto
    {
        public string Type { get; set; }


        public DateTime CreatedOn { get; set; }

        public bool Deleted { get; set; }

        /// <summary>
        /// Our internal ID to reference the construction.
        /// </summary>
        public Guid ConstructionId { get; set; }

        /// <summary>
        /// The Zone this construction belongs to.
        /// Can be left null (If this is the case, 'IsAggregateData' should be TRUE!)
        /// </summary>
        public Guid? ZoneId { get; set; }

        /// <summary>
        /// This is the value that is searched/displayed in the admin portal.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// This is the value that is displayed in the PDF report.
        /// </summary>
        public string DisplayDescription { get; set; }

        public string ConstructionCategoryCode { get; set; }
        public string ConstructionCategoryTitle { get; set; }
        public string ConstructionSubCategoryCode { get; set; }
        public string ConstructionSubCategoryTitle { get; set; }
        public string OpeningStyleCode { get; set; }
        public string OpeningStyleTitle { get; set; }
        public string Comments { get; set; }

        /// <summary>
        /// Manufacturer
        /// </summary>
        public string ManufacturerDescription { get; set; }

        /// <summary>
        /// Our internal ID to reference the manufacturer.
        /// </summary>
        public Guid? ManufacturerId { get; set; }

        private string _performanceJson;
        [JsonIgnore]
        public string PerformanceJson
        {
            get => _performanceJson;
            set
            {
                _performanceJson = value;
                _performance = !string.IsNullOrEmpty(_performanceJson) && _performanceJson != "DO_NOT_PROCESS"
                    ? JsonConvert.DeserializeObject<OpeningPerformance>(_performanceJson)
                    : new OpeningPerformance();
            }
        }

        private OpeningPerformance _performance;
        public OpeningPerformance Performance
        {
            get => _performance;
            set
            {
                _performance = value;
                if (_performance != null)
                {
                    _performanceJson = JsonConvert.SerializeObject(_performance);
                }
            }
        }

        /// <summary>
        /// Whether this construction is marked as a favorite
        /// </summary>
        public bool IsFavourite { get; set; }

        /// <summary>
        /// Insulation description for display in list
        /// </summary>
        public string InsulationDescription { get; set; }

        /// <summary>
        /// Glass Type title for display in list (opening records only)
        /// </summary>
        public string GlassTypeTitle { get; set; }

        /// <summary>
        /// Glass Colour title for display in list (opening records only)
        /// </summary>
        public string GlassColourTitle { get; set; }

        /// <summary>
        /// Low-E Coating for display in list (opening records only)
        /// </summary>
        public string LowECoating { get; set; }
    }
}