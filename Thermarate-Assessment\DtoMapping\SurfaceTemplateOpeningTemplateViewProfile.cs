﻿using AutoMapper;
using RediSoftware.Dtos;
using RediSoftware.Models;
using AutoMapper.QueryableExtensions;

namespace RediSoftware.DtoMapping
{
    public class SurfaceTemplateOpeningTemplateViewProfile : Profile
    {
        public SurfaceTemplateOpeningTemplateViewProfile()
        {
            // Map from Table to DTO
            CreateMap<RSS_SurfaceTemplateOpeningTemplateView, SurfaceOpeningViewDto>()
                .ForMember(dest => dest.InsulationDescription, opt => opt.MapFrom(src => src.InsulationDescription))
                .ForMember(dest => dest.GlassTypeTitle, opt => opt.MapFrom(src => src.GlassTypeTitle))
                .ForMember(dest => dest.GlassColourTitle, opt => opt.MapFrom(src => src.GlassColourTitle))
                .ForMember(dest => dest.LowECoating, opt => opt.MapFrom(src => src.LowECoating))
            ;
        }
    }
}