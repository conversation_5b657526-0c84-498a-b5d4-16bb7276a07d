USE [thermarate];

-- Update RSS_SurfaceTemplateOpeningTemplateView to include Glass Type, Glass Colour, and Low-E fields for openings
DROP VIEW dbo.RSS_SurfaceTemplateOpeningTemplateView
GO

CREATE VIEW dbo.RSS_SurfaceTemplateOpeningTemplateView
AS
SELECT
    'surface' AS [Type],
    ConstructionId,
    surface.[Description],
    DisplayDescription,
    cc.Title as ConstructionCategoryTitle,
    cc.ConstructionCategoryCode,
    csc.Title as ConstructionSubCategoryTitle,
    csc.ConstructionSubCategoryCode,
    os.Title as OpeningStyleTitle,
    os.OpeningStyleCode,
    manufacturer.[Description] AS ManufacturerDescription,
    surface.ManufacturerId,
    Comments,
    surface.[Deleted],
    surface.[CreatedOn],
    'DO_NOT_PROCESS' as PerformanceJson, -- sigh
    surface.[IsFavourite],
    -- Add insulation data fields
    CASE
        WHEN surface.InsulationDataJson IS NOT NULL AND surface.InsulationDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(surface.InsulationDataJson, '$.HasData') = 'true'
                THEN JSON_VALUE(surface.InsulationDataJson, '$.Description')
                ELSE 'Not Specified'
            END
        ELSE 'Not Specified'
    END as InsulationDescription,
    -- Glass fields are not applicable for surfaces, set to NULL
    NULL as GlassTypeTitle,
    NULL as GlassColourTitle,
    NULL as LowECoating
FROM
    dbo.RSS_SurfaceTemplate as surface
        JOIN dbo.RSS_ConstructionCategory cc ON cc.ConstructionCategoryCode = surface.ConstructionCategoryCode
        LEFT JOIN dbo.RSS_ConstructionSubCategory csc ON csc.ConstructionSubCategoryCode = surface.ConstructionSubCategoryCode
        LEFT JOIN dbo.RSS_OpeningStyle os ON os.OpeningStyleCode = surface.OpeningStyleCode
        LEFT JOIN dbo.RSS_Manufacturer manufacturer ON manufacturer.ManufacturerId = surface.ManufacturerId

UNION ALL

SELECT
    'opening' AS [Type],
    ConstructionId,
    opening.[Description],
    DisplayDescription,
    cc.Title as ConstructionCategoryTitle,
    cc.ConstructionCategoryCode,
    csc.Title as ConstructionSubCategoryTitle,
    csc.ConstructionSubCategoryCode,
    os.Title as OpeningStyleTitle,
    os.OpeningStyleCode,
    manufacturer.[Description] AS ManufacturerDescription,
    opening.ManufacturerId,
    Comments,
    opening.[Deleted],
    opening.[CreatedOn],
    opening.PerformanceJson,
    opening.[IsFavourite],
    -- Openings don't have insulation data, so set defaults
    NULL as InsulationDescription,
    -- Extract Glass Type from GlassDataJson
    CASE
        WHEN opening.GlassDataJson IS NOT NULL AND opening.GlassDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.GlassDataJson, '$.Type.Title') IS NOT NULL
                THEN JSON_VALUE(opening.GlassDataJson, '$.Type.Title')
                ELSE 'Not Specified'
            END
        ELSE 'Not Specified'
    END as GlassTypeTitle,
    -- Extract Glass Colour from GlassDataJson
    CASE
        WHEN opening.GlassDataJson IS NOT NULL AND opening.GlassDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.GlassDataJson, '$.Colour.Title') IS NOT NULL
                THEN JSON_VALUE(opening.GlassDataJson, '$.Colour.Title')
                ELSE 'Not Specified'
            END
        ELSE 'Not Specified'
    END as GlassColourTitle,
    -- Extract Low-E Coating from GlassDataJson
    CASE
        WHEN opening.GlassDataJson IS NOT NULL AND opening.GlassDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.GlassDataJson, '$.HasLowECoating') = 'true'
                THEN 'Yes'
                WHEN JSON_VALUE(opening.GlassDataJson, '$.HasLowECoating') = 'false'
                THEN 'No'
                ELSE 'Not Specified'
            END
        ELSE 'Not Specified'
    END as LowECoating
FROM
    dbo.RSS_OpeningTemplate as opening
        JOIN dbo.RSS_ConstructionCategory cc ON cc.ConstructionCategoryCode = opening.ConstructionCategoryCode
        LEFT JOIN dbo.RSS_ConstructionSubCategory csc ON csc.ConstructionSubCategoryCode = opening.ConstructionSubCategoryCode
        LEFT JOIN dbo.RSS_OpeningStyle os ON os.OpeningStyleCode = opening.OpeningStyleCode
        LEFT JOIN dbo.RSS_Manufacturer manufacturer ON manufacturer.ManufacturerId = opening.ManufacturerId
GO
